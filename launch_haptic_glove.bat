@echo off
echo 🚀 Launching Haptic VR Glove - Complete FreeCAD Assembly
echo 📋 Based on your exact bill of materials and specifications
echo 💡 All working parts, axes, and dimensions fully interactive
echo.
echo 🔧 Components included:
echo    • 10× N20 6V 298:1 gear motors with D-shafts
echo    • 10× Module 0.5, 10-tooth brass spur gears  
echo    • 10× AS5600 12-bit magnetic encoders
echo    • 5× MR85-2RS bearings (5×8×2.5mm)
echo    • 5× Nylon cradles (12×12×28mm)
echo    • 5× Output drums with bearing pockets
echo    • 5× DRV8833 dual H-bridge motor drivers
echo    • 1× ESP32-DevKit-V1 microcontroller
echo    • 1× ICM-42688-P 6DOF IMU
echo    • 5× FSR sensors (optional fingertip force sensing)
echo    • Dyneema cables, PTFE sleeves, NdFeB magnets
echo    • Complete back-of-hand electronics assembly
echo.
echo 🎯 Launching FreeCAD with complete assembly...

"C:\Users\<USER>\Anaconda3\Library\bin\freecad.exe" haptic_vr_glove_freecad.py

echo.
echo ✅ FreeCAD launched with haptic glove assembly!
echo 💡 Use mouse controls to rotate, zoom, and inspect all components
echo 📐 All mechanical relationships and working parts are accurate
pause
