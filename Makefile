# LucidGlove v6 - FreeCAD Build System
# Fully automated build process for the haptic glove project

.PHONY: build clean help install-freecad check-freecad

# Default target
build: check-freecad
	@echo "🚀 Building LucidGlove v6 project..."
	@echo "📐 Creating parametric FreeCAD model with all components"
	FreeCADCmd create_haptic_glove_project.py
	@echo "✅ Build complete! Files created:"
	@echo "   📁 LucidGlove_v6.FCStd (FreeCAD project file)"
	@if exist LucidGlove_v6.stl echo "   📦 LucidGlove_v6.stl (3D printable mesh)"
	@echo ""
	@echo "💡 To view the project:"
	@echo "   1. Open FreeCAD GUI"
	@echo "   2. File → Open → LucidGlove_v6.FCStd"
	@echo "   3. Explore the parametric model"

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	@if exist LucidGlove_v6.FCStd del /Q LucidGlove_v6.FCStd
	@if exist LucidGlove_v6.stl del /Q LucidGlove_v6.stl
	@echo "✅ Clean complete!"

# Check if FreeCAD is installed and accessible
check-freecad:
	@echo "🔍 Checking FreeCAD installation..."
	@FreeCADCmd --version >nul 2>&1 || (echo "❌ FreeCAD not found! Please install FreeCAD and add it to PATH" && exit /b 1)
	@echo "✅ FreeCAD found and ready"

# Install FreeCAD (Windows - requires admin privileges)
install-freecad:
	@echo "📦 Installing FreeCAD..."
	@echo "⚠️  This requires administrator privileges"
	@echo "🌐 Downloading FreeCAD installer..."
	@powershell -Command "Invoke-WebRequest -Uri 'https://github.com/FreeCAD/FreeCAD/releases/download/0.21.2/FreeCAD-0.21.2-WIN-x64-installer-1.exe' -OutFile 'FreeCAD-installer.exe'"
	@echo "🚀 Running installer (follow the prompts)..."
	@FreeCAD-installer.exe
	@del /Q FreeCAD-installer.exe
	@echo "✅ FreeCAD installation complete!"
	@echo "🔧 Please restart your terminal to refresh PATH"

# Development build with verbose output
dev-build: check-freecad
	@echo "🔧 Development build with verbose output..."
	FreeCADCmd --log-file build.log create_haptic_glove_project.py
	@echo "📋 Build log saved to build.log"

# Export STL only (requires existing FCStd file)
export-stl: check-freecad
	@echo "📦 Exporting STL mesh..."
	@if not exist LucidGlove_v6.FCStd (echo "❌ LucidGlove_v6.FCStd not found! Run 'make build' first" && exit /b 1)
	FreeCADCmd -c "import FreeCAD; doc=FreeCAD.openDocument('LucidGlove_v6.FCStd'); import ImportGui; ImportGui.export(doc.Objects, 'LucidGlove_v6.stl'); print('STL exported successfully')"
	@echo "✅ STL export complete: LucidGlove_v6.stl"

# Quick rebuild (clean + build)
rebuild: clean build

# Help target
help:
	@echo "🔧 LucidGlove v6 - FreeCAD Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  build          - Build the complete FreeCAD project (default)"
	@echo "  clean          - Remove all build artifacts"
	@echo "  rebuild        - Clean and build"
	@echo "  dev-build      - Build with verbose logging"
	@echo "  export-stl     - Export STL mesh from existing project"
	@echo "  check-freecad  - Verify FreeCAD installation"
	@echo "  install-freecad- Install FreeCAD (Windows, requires admin)"
	@echo "  help           - Show this help message"
	@echo ""
	@echo "📋 Project specifications:"
	@echo "  • 5× N20-based finger modules"
	@echo "  • Parametric design with user-tweakable dimensions"
	@echo "  • Complete bill of materials integration"
	@echo "  • Ready for 3D printing and assembly"
	@echo ""
	@echo "🚀 Quick start: make build"
