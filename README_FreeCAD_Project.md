# Haptic VR Glove - FreeCAD Project

## 📁 Files Included

### Main Project Creator
- **`create_haptic_glove_project.py`** - Main script to create FreeCAD project file
- **`create_project.bat`** - Batch file to easily run the project creator

### Alternative Scripts  
- **`haptic_vr_glove_freecad.py`** - Direct FreeCAD script (run inside FreeCAD)
- **`launch_haptic_glove.bat`** - Launch the direct script

## 🚀 How to Create Your Project File

### Method 1: Create .FCStd Project File (Recommended)
1. **Double-click** `create_project.bat`
2. **Wait** for FreeCAD to process (may take 30-60 seconds)
3. **Look for** `HapticVRGlove.FCStd` file in this folder
4. **Open** the .FCStd file in FreeCAD

### Method 2: Run Script Directly in FreeCAD
1. **Open FreeCAD**
2. **Go to:** File → Open
3. **Select:** `create_haptic_glove_project.py`
4. **Or:** Copy script contents into FreeCAD Python console

## 🔧 What You'll Get

### Complete Assembly with All BOM Components:
- ✅ **10× N20 6V 298:1 gear motors** with D-shafts and mounting holes
- ✅ **10× Module 0.5, 10-tooth brass spur gears** with 3mm bores  
- ✅ **10× AS5600 12-bit magnetic encoders** with 2mm sensing gaps
- ✅ **5× MR85-2RS bearings** (5×8×2.5mm) with seal grooves
- ✅ **5× Nylon cradles** (12×12×28mm) for proximal phalanx mounting
- ✅ **5× Output drums** with integrated bearing pockets
- ✅ **1× ESP32-DevKit-V1 microcontroller**
- ✅ **Dyneema cables** (0.8mm) for force transmission
- ✅ **4×2mm NdFeB magnets** for encoder sensing
- ✅ **Complete mechanical assembly** with proper spacing

### Interactive Features:
- 🎯 **Fully interactive 3D model** - rotate, zoom, inspect
- 📐 **Accurate dimensions** from your specifications  
- 🎨 **Color-coded materials** (brass, steel, nylon, PCBs)
- 🔧 **Proper mechanical relationships** and clearances
- 📊 **Component tree** with detailed labels
- 📏 **All axes and rotation points** correctly positioned

## 💡 FreeCAD Usage Tips

### Navigation:
- **Mouse wheel:** Zoom in/out
- **Middle mouse drag:** Rotate view  
- **Shift + middle mouse:** Pan view
- **Click components** in tree view to highlight
- **Right-click** for measurement tools

### Analysis:
- **Measure distances** between components
- **Check clearances** and fits
- **Verify mechanical relationships**
- **Export individual parts** as STL for 3D printing
- **Generate technical drawings**

## 📋 Bill of Materials Verification

This project implements your exact BOM:
- **Motors:** N20 6V 298:1 (26×10×12mm) with D-shaft
- **Gears:** Module 0.5, 10-tooth brass (6mm OD, 3mm bore)
- **Bearings:** MR85-2RS (5×8×2.5mm) double sealed
- **Encoders:** AS5600 12-bit magnetic with 4×2mm magnets
- **Cradles:** 12×12×28mm nylon housing with M2×8 mounting
- **Drums:** 6mm thick with bearing pocket and cable attachment
- **Electronics:** ESP32 + main PCB assembly

## 🔍 Troubleshooting

### If FreeCAD doesn't start:
1. Check FreeCAD installation path in batch files
2. Update path to your FreeCAD executable
3. Run FreeCAD manually and open script

### If project file isn't created:
1. Check write permissions in folder
2. Run FreeCAD as administrator
3. Use Method 2 (direct script execution)

## ⚡ Ready for Manufacturing

This project provides everything needed for:
- **3D printing** the nylon cradles and drums
- **Ordering** exact components from your BOM
- **Assembly planning** with proper component relationships
- **Mechanical verification** before manufacturing
- **Technical documentation** generation

All components are positioned exactly as they will be in the final assembly!
