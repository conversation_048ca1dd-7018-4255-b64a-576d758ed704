@echo off
echo 🔧 Haptic VR Glove - FreeCAD Project Creator
echo 📐 This will run the Python script inside FreeCAD
echo.

REM Try different FreeCAD installation paths
set FREECAD_PATH=""

REM Check Program Files
if exist "C:\Program Files\FreeCAD 1.0\bin\FreeCAD.exe" (
    set FREECAD_PATH="C:\Program Files\FreeCAD 1.0\bin\FreeCAD.exe"
    goto :found
)

REM Check Program Files (x86)
if exist "C:\Program Files (x86)\FreeCAD 1.0\bin\FreeCAD.exe" (
    set FREECAD_PATH="C:\Program Files (x86)\FreeCAD 1.0\bin\FreeCAD.exe"
    goto :found
)

REM Check Anaconda installation
if exist "C:\Users\<USER>\Anaconda3\Library\bin\freecad.exe" (
    set FREECAD_PATH="C:\Users\<USER>\Anaconda3\Library\bin\freecad.exe"
    goto :found
)

echo ❌ FreeCAD not found in standard locations
echo 💡 Please install FreeCAD or update the path in this batch file
pause
exit /b 1

:found
echo ✅ Found FreeCAD at: %FREECAD_PATH%
echo 🚀 Starting FreeCAD with the haptic glove project script...
echo.

REM Start FreeCAD and run the script
%FREECAD_PATH% create_haptic_glove_project.py

echo.
echo 💡 Instructions:
echo    1. FreeCAD should now be open
echo    2. Go to Macro → Macros...
echo    3. Click "Execute" to run the script
echo    4. The project will be created and saved as HapticVRGlove.FCStd
echo.
pause
