#!/usr/bin/env python3
"""
Haptic VR Glove - FreeCAD Project Generator
Creates a .FCStd project file that can be opened directly in FreeCAD
This version works without requiring FreeCAD to be running
"""

import os
import zipfile
import xml.etree.ElementTree as ET
from xml.dom import minidom
import json

def create_freecad_document_xml():
    """Create the main Document.xml file for FreeCAD project"""

    # Root element with proper attributes
    root = ET.Element("Document")
    root.set("SchemaVersion", "4")
    root.set("ProgramVersion", "0.21")
    root.set("FileVersion", "1")

    # Properties section
    properties = ET.SubElement(root, "Properties")
    properties.set("Count", "5")

    # Add document properties with proper structure
    prop1 = ET.SubElement(properties, "Property")
    prop1.set("name", "Comment")
    prop1.set("type", "App::PropertyString")
    string1 = ET.SubElement(prop1, "String")
    string1.set("value", "Haptic VR Glove - Complete Project")

    prop2 = ET.SubElement(properties, "Property")
    prop2.set("name", "Company")
    prop2.set("type", "App::PropertyString")
    string2 = ET.SubElement(prop2, "String")
    string2.set("value", "Raptor VR")

    prop3 = ET.SubElement(properties, "Property")
    prop3.set("name", "CreatedBy")
    prop3.set("type", "App::PropertyString")
    string3 = ET.SubElement(prop3, "String")
    string3.set("value", "Haptic Glove Generator")

    prop4 = ET.SubElement(properties, "Property")
    prop4.set("name", "Label")
    prop4.set("type", "App::PropertyString")
    string4 = ET.SubElement(prop4, "String")
    string4.set("value", "HapticVRGlove")

    prop5 = ET.SubElement(properties, "Property")
    prop5.set("name", "LastModifiedBy")
    prop5.set("type", "App::PropertyString")
    string5 = ET.SubElement(prop5, "String")
    string5.set("value", "Auto-Generated")
    
    # Objects section - we'll add our components here
    objects = ET.SubElement(root, "Objects")
    objects.set("Count", "42")  # 5 fingers × 8 components + ESP32 + PCB

    # Add finger components (5 fingers × 8 components each)
    component_types = [
        ("Cradle", "Part::Feature", "Nylon cradle housing"),
        ("Motor", "Part::Feature", "N20 6V 298:1 gear motor"),
        ("Gear", "Part::Feature", "Module 0.5, 10-tooth brass gear"),
        ("Drum", "Part::Feature", "6mm output drum with bearing pocket"),
        ("Bearing", "Part::Feature", "MR85-2RS bearing (5×8×2.5mm)"),
        ("Encoder", "Part::Feature", "AS5600 12-bit magnetic encoder"),
        ("Magnet", "Part::Feature", "4×2mm NdFeB magnet"),
        ("Cable", "Part::Feature", "Dyneema cable (Ø0.8mm)")
    ]

    obj_count = 0
    for finger in range(1, 6):  # Fingers 1-5
        for comp_name, comp_type, comp_desc in component_types:
            obj_name = f"{comp_name}_{finger}"
            obj = ET.SubElement(objects, "Object")
            obj.set("type", comp_type)
            obj.set("name", obj_name)

            # Add properties for each component
            props = ET.SubElement(obj, "Properties")
            props.set("Count", "3")

            # Label property
            label_prop = ET.SubElement(props, "Property")
            label_prop.set("name", "Label")
            label_prop.set("type", "App::PropertyString")
            label_string = ET.SubElement(label_prop, "String")
            label_string.set("value", f"{obj_name} ({comp_desc})")

            # Placement property (position in 3D space)
            place_prop = ET.SubElement(props, "Property")
            place_prop.set("name", "Placement")
            place_prop.set("type", "App::PropertyPlacement")

            # Position each finger unit with proper spacing
            x_pos = 0
            y_pos = (finger - 1) * 22.0  # 22mm finger spacing
            z_pos = 0

            # Adjust position based on component type
            if comp_name == "Motor":
                x_pos += 1
                y_pos += 1
                z_pos += 0.5
            elif comp_name == "Gear":
                x_pos += 28
                z_pos += 6
            elif comp_name == "Drum":
                x_pos += 30
                z_pos += 6
            elif comp_name == "Encoder":
                x_pos += 45
                z_pos += 8

            placement = ET.SubElement(place_prop, "PropertyPlacement")
            placement.set("Axis", "0 0 1")
            placement.set("Angle", "0")
            placement.set("Position", f"{x_pos} {y_pos} {z_pos}")

            # Shape property (basic geometry)
            shape_prop = ET.SubElement(props, "Property")
            shape_prop.set("name", "Shape")
            shape_prop.set("type", "Part::PropertyPartShape")
            part_elem = ET.SubElement(shape_prop, "Part")
            part_elem.set("file", f"{obj_name}.brp")

            obj_count += 1
    
    # Add ESP32 controller
    esp32_obj = ET.SubElement(objects, "Object")
    esp32_obj.set("type", "Part::Feature")
    esp32_obj.set("name", "ESP32_Main")

    esp32_props = ET.SubElement(esp32_obj, "Properties")
    esp32_props.set("Count", "3")

    esp32_label = ET.SubElement(esp32_props, "Property")
    esp32_label.set("name", "Label")
    esp32_label.set("type", "App::PropertyString")
    esp32_string = ET.SubElement(esp32_label, "String")
    esp32_string.set("value", "ESP32_Main (ESP32-WROOM-32)")

    esp32_place = ET.SubElement(esp32_props, "Property")
    esp32_place.set("name", "Placement")
    esp32_place.set("type", "App::PropertyPlacement")
    esp32_placement = ET.SubElement(esp32_place, "PropertyPlacement")
    esp32_placement.set("Axis", "0 0 1")
    esp32_placement.set("Angle", "0")
    esp32_placement.set("Position", "-80 44 20")

    esp32_shape = ET.SubElement(esp32_props, "Property")
    esp32_shape.set("name", "Shape")
    esp32_shape.set("type", "Part::PropertyPartShape")
    esp32_part = ET.SubElement(esp32_shape, "Part")
    esp32_part.set("file", "ESP32_Main.brp")

    # Add Main PCB
    pcb_obj = ET.SubElement(objects, "Object")
    pcb_obj.set("type", "Part::Feature")
    pcb_obj.set("name", "Main_PCB")

    pcb_props = ET.SubElement(pcb_obj, "Properties")
    pcb_props.set("Count", "3")

    pcb_label = ET.SubElement(pcb_props, "Property")
    pcb_label.set("name", "Label")
    pcb_label.set("type", "App::PropertyString")
    pcb_string = ET.SubElement(pcb_label, "String")
    pcb_string.set("value", "Main PCB (70×50mm)")

    pcb_place = ET.SubElement(pcb_props, "Property")
    pcb_place.set("name", "Placement")
    pcb_place.set("type", "App::PropertyPlacement")
    pcb_placement = ET.SubElement(pcb_place, "PropertyPlacement")
    pcb_placement.set("Axis", "0 0 1")
    pcb_placement.set("Angle", "0")
    pcb_placement.set("Position", "-90 34 15")

    pcb_shape = ET.SubElement(pcb_props, "Property")
    pcb_shape.set("name", "Shape")
    pcb_shape.set("type", "Part::PropertyPartShape")
    pcb_part = ET.SubElement(pcb_shape, "Part")
    pcb_part.set("file", "Main_PCB.brp")
    
    return root

def create_gui_document_xml():
    """Create the GuiDocument.xml file for FreeCAD project"""

    root = ET.Element("Document")
    root.set("SchemaVersion", "1")

    # ViewProvider section for visual properties
    viewproviders = ET.SubElement(root, "ViewProviderData")
    viewproviders.set("Count", "42")
    
    # Add view providers for all components
    component_names = []
    
    # Add finger components
    for finger in range(1, 6):
        for comp_type in ["Cradle", "Motor", "Gear", "Drum", "Bearing", "Encoder", "Magnet", "Cable"]:
            component_names.append(f"{comp_type}_{finger}")
    
    # Add electronics
    component_names.extend(["ESP32_Main", "Main_PCB"])
    
    # Define colors for different component types
    colors = {
        "Cradle": "0.9 0.9 0.7",      # Nylon color
        "Motor": "0.2 0.2 0.2",       # Dark gray
        "Gear": "1.0 0.84 0.0",       # Gold (brass)
        "Drum": "0.0 0.4 0.8",        # Blue (3D printed)
        "Bearing": "0.7 0.7 0.7",     # Steel gray
        "Encoder": "0.0 0.6 0.0",     # Green PCB
        "Magnet": "0.3 0.3 0.3",      # Dark metallic
        "Cable": "1.0 1.0 0.0",       # Yellow
        "ESP32": "0.0 0.6 0.0",       # Green PCB
        "Main_PCB": "0.0 0.6 0.0"     # Green PCB
    }
    
    for comp_name in component_names:
        vp = ET.SubElement(viewproviders, "ViewProvider")
        vp.set("name", comp_name)
        vp.set("expanded", "0")

        # Determine color based on component type
        comp_type = comp_name.split('_')[0]
        if comp_type in colors:
            color = colors[comp_type]
        elif "ESP32" in comp_name:
            color = colors["ESP32"]
        elif "PCB" in comp_name:
            color = colors["Main_PCB"]
        else:
            color = "0.8 0.8 0.8"  # Default gray

        # Add visual properties
        props = ET.SubElement(vp, "Properties")
        props.set("Count", "2")

        # Shape color
        color_prop = ET.SubElement(props, "Property")
        color_prop.set("name", "ShapeColor")
        color_prop.set("type", "App::PropertyColor")
        color_elem = ET.SubElement(color_prop, "PropertyColor")
        color_elem.set("value", color)

        # Visibility
        vis_prop = ET.SubElement(props, "Property")
        vis_prop.set("name", "Visibility")
        vis_prop.set("type", "App::PropertyBool")
        bool_elem = ET.SubElement(vis_prop, "Bool")
        bool_elem.set("value", "true")
    
    return root

def create_basic_brp_files():
    """Create proper .brp (BREP) geometry files for components"""
    brp_files = {}

    # Create proper BREP format for a simple box
    def create_box_brep(length, width, height):
        return f"""DBRep_DrawableShape

CASCADE Topology V1, (c) Matra-Datavision
Locations 0
Curve2ds 12
1 0 0 {length}
1 0 0 {width}
1 0 0 {length}
1 0 0 {width}
1 0 0 {height}
1 0 0 {height}
1 0 0 {height}
1 0 0 {height}
1 0 0 {length}
1 0 0 {width}
1 0 0 {length}
1 0 0 {width}
Curves 12
1 0 0 0 1 0 0
1 {length} 0 0 0 1 0
1 {length} {width} 0 -1 0 0
1 0 {width} 0 0 -1 0
1 0 0 0 0 0 1
1 {length} 0 0 0 0 1
1 {length} {width} 0 0 0 1
1 0 {width} 0 0 0 1
1 0 0 {height} 1 0 0
1 {length} 0 {height} 0 1 0
1 {length} {width} {height} -1 0 0
1 0 {width} {height} 0 -1 0
Polygon3D 0
PolygonOnTriangulations 0
Surfaces 6
1 0 0 0 0 0 1 1 0 0 0 1 0
1 {length} 0 0 -1 0 0 0 0 1 0 1 0
1 0 {width} 0 0 -1 0 1 0 0 0 0 1
1 0 0 0 1 0 0 0 0 1 0 1 0
1 0 0 0 0 1 0 0 0 1 1 0 0
1 0 0 {height} 0 0 -1 1 0 0 0 1 0
Triangulations 0

TShapes 26
Ve
1e-07
0 0 0
*
Ve
1e-07
{length} 0 0
*
Ve
1e-07
{length} {width} 0
*
Ve
1e-07
0 {width} 0
*
Ve
1e-07
0 0 {height}
*
Ve
1e-07
{length} 0 {height}
*
Ve
1e-07
{length} {width} {height}
*
Ve
1e-07
0 {width} {height}
*
Ed
1e-07 1 1 0
1  1 0 0 {length}
2  1 1 0 0 {length}
2  2 2 0 0 {width}
0

*
Ed
1e-07 1 1 0
1  2 0 0 {width}
2  3 1 0 0 {width}
2  4 3 0 0 {length}
0

*
Ed
1e-07 1 1 0
1  3 0 0 {length}
2  5 2 0 0 {length}
2  6 4 0 0 {width}
0

*
Ed
1e-07 1 1 0
1  4 0 0 {width}
2  7 3 0 0 {width}
2  8 1 0 0 {length}
0

*
Ed
1e-07 1 1 0
1  5 0 0 {height}
2  9 5 0 0 {height}
2  10 6 0 0 {height}
0

*
Ed
1e-07 1 1 0
1  6 0 0 {height}
2  11 6 0 0 {height}
2  12 7 0 0 {height}
0

*
Ed
1e-07 1 1 0
1  7 0 0 {height}
2  13 7 0 0 {height}
2  14 8 0 0 {height}
0

*
Ed
1e-07 1 1 0
1  8 0 0 {height}
2  15 8 0 0 {height}
2  16 5 0 0 {height}
0

*
Ed
1e-07 1 1 0
1  9 0 0 {length}
2  17 9 0 0 {length}
2  18 10 0 0 {width}
0

*
Ed
1e-07 1 1 0
1  10 0 0 {width}
2  19 10 0 0 {width}
2  20 11 0 0 {length}
0

*
Ed
1e-07 1 1 0
1  11 0 0 {length}
2  21 11 0 0 {length}
2  22 12 0 0 {width}
0

*
Ed
1e-07 1 1 0
1  12 0 0 {width}
2  23 12 0 0 {width}
2  24 9 0 0 {length}
0

*
Wi

*
Wi

*
Wi

*
Wi

*
Wi

*
Wi

*
Fa
0  1e-07 1 0
2  1
0  1 1 0 2  1
1  1 0 1 2  2
1  2 0 1 2  3
1  3 0 1 2  4
0

*
Fa
0  1e-07 2 0
2  2
0  1 2 0 2  2
1  5 0 1 2  6
1  6 0 1 2  7
1  7 0 1 2  8
0

*
Fa
0  1e-07 3 0
2  3
0  1 3 0 2  3
1  9 0 1 2  10
1  10 0 1 2  11
1  11 0 1 2  12
0

*
Fa
0  1e-07 4 0
2  4
0  1 4 0 2  4
1  13 1 1 2  14
1  14 1 1 2  15
1  15 1 1 2  16
0

*
Fa
0  1e-07 5 0
2  5
0  1 5 0 2  5
1  17 0 1 2  18
1  18 0 1 2  19
1  19 0 1 2  20
0

*
Fa
0  1e-07 6 0
2  6
0  1 6 0 2  6
1  21 1 1 2  22
1  22 1 1 2  23
1  23 1 1 2  24
0

*
Sh
1e-07 1 1 0
2  7
1  1 0 1 2  1
1  2 0 1 2  2
1  3 0 1 2  3
1  4 0 1 2  4
1  5 0 1 2  5
1  6 0 1 2  6
0

*
So
1e-07 1 1 0
2  1
0

*
""".encode('utf-8')

    # Create cylinder BREP for round components
    def create_cylinder_brep(radius, height):
        return f"""DBRep_DrawableShape

CASCADE Topology V1, (c) Matra-Datavision
Locations 0
Curve2ds 4
1 0 0 6.28318530718
1 0 0 6.28318530718
2 0 0 1 0 {radius} 0
2 0 0 1 0 {radius} 0
Curves 3
1 0 0 0 0 0 1
1 0 0 {height} 0 0 1
2 0 0 0 0 0 1 {radius} 0 0 1 0 0
Polygon3D 0
PolygonOnTriangulations 0
Surfaces 3
2 0 0 0 0 0 1 1 0 0 0 1 0 {radius}
1 0 0 0 0 0 1 1 0 0 0 1 0
1 0 0 {height} 0 0 -1 1 0 0 0 1 0
Triangulations 0

TShapes 9
Ve
1e-07
0 0 0
*
Ve
1e-07
0 0 {height}
*
Ed
1e-07 1 1 0
1  1 0 0 {height}
0

*
Ed
1e-07 1 1 0
1  3 0 0 6.28318530718
2  1 1 0 0 6.28318530718
2  2 2 0 0 6.28318530718
0

*
Ed
1e-07 1 1 0
1  2 0 0 6.28318530718
2  3 3 0 0 6.28318530718
2  4 4 0 0 6.28318530718
0

*
Wi

*
Wi

*
Wi

*
Fa
0  1e-07 1 0
2  1
0  1 1 0 2  1
1  1 0 1 2  2
0

*
Fa
0  1e-07 2 0
2  2
0  1 2 0 2  2
1  2 0 1 2  3
0

*
Fa
0  1e-07 3 0
2  3
0  1 3 0 2  3
1  3 1 1 2  4
0

*
Sh
1e-07 1 1 0
2  3
1  1 0 1 2  1
1  2 0 1 2  2
1  3 0 1 2  3
0

*
So
1e-07 1 1 0
2  1
0

*
""".encode('utf-8')

    # Component dimensions and shapes
    components = {
        # Finger components (boxes)
        "Cradle": (28, 12, 12, "box"),
        "Motor": (26, 10, 12, "box"),
        "Drum": (10, 10, 6, "cylinder"),
        "Bearing": (8, 8, 2.5, "cylinder"),
        "Encoder": (15, 15, 1.6, "box"),
        "Magnet": (4, 4, 2, "cylinder"),
        "Gear": (6, 6, 2, "cylinder"),
        "Cable": (0.8, 0.8, 50, "cylinder"),
        # Electronics (boxes)
        "ESP32_Main": (55, 28, 8, "box"),
        "Main_PCB": (70, 50, 1.6, "box")
    }

    # Create .brp files for all components
    for finger in range(1, 6):
        for comp_type in ["Cradle", "Motor", "Gear", "Drum", "Bearing", "Encoder", "Magnet", "Cable"]:
            comp_name = f"{comp_type}_{finger}"
            dims = components[comp_type]

            if dims[3] == "box":
                brp_files[f"{comp_name}.brp"] = create_box_brep(dims[0], dims[1], dims[2])
            else:  # cylinder
                brp_files[f"{comp_name}.brp"] = create_cylinder_brep(dims[0]/2, dims[2])

    # Add electronics
    for comp_type in ["ESP32_Main", "Main_PCB"]:
        dims = components[comp_type]
        brp_files[f"{comp_type}.brp"] = create_box_brep(dims[0], dims[1], dims[2])

    return brp_files

def create_freecad_project_file(filename="HapticVRGlove.FCStd"):
    """Create a complete FreeCAD project file"""
    
    print("🚀 Creating FreeCAD project file...")
    print("📋 Generating XML structure...")
    
    # Create the main document XML
    doc_xml = create_freecad_document_xml()
    doc_xml_str = minidom.parseString(ET.tostring(doc_xml)).toprettyxml(indent="  ")
    
    # Create the GUI document XML
    gui_xml = create_gui_document_xml()
    gui_xml_str = minidom.parseString(ET.tostring(gui_xml)).toprettyxml(indent="  ")
    
    # Create basic geometry files
    brp_files = create_basic_brp_files()
    
    print("📦 Creating FreeCAD archive...")
    
    # Create the .FCStd file (which is actually a ZIP archive)
    with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as fcstd:
        # Add main document
        fcstd.writestr("Document.xml", doc_xml_str)
        
        # Add GUI document
        fcstd.writestr("GuiDocument.xml", gui_xml_str)
        
        # Add geometry files
        for brp_name, brp_content in brp_files.items():
            fcstd.writestr(brp_name, brp_content)
    
    print(f"✅ FreeCAD project created: {filename}")
    return filename

if __name__ == "__main__":
    print("🔧 Haptic VR Glove - FreeCAD Project Generator")
    print("📐 Creating .FCStd project file...")
    print()
    
    project_file = create_freecad_project_file()
    
    print()
    print("🎉 SUCCESS! FreeCAD project file created!")
    print(f"📁 File: {project_file}")
    print("📊 Components included:")
    print("   • 5× Complete finger units (40 components total)")
    print("   • 1× ESP32-DevKit-V1 controller")
    print("   • 1× Main PCB (70×50mm)")
    print()
    print("💡 To use this project:")
    print("   1. Open FreeCAD")
    print("   2. File → Open → HapticVRGlove.FCStd")
    print("   3. All components will be visible in the 3D view")
    print("   4. Use the tree view to select and inspect components")
    print("   5. Components are positioned with proper mechanical spacing")
    print()
    print("🔧 Each finger unit contains:")
    print("   • N20 6V 298:1 gear motor")
    print("   • Module 0.5, 10-tooth brass spur gear")
    print("   • 6mm output drum with bearing pocket")
    print("   • MR85-2RS bearing (5×8×2.5mm)")
    print("   • AS5600 12-bit magnetic encoder")
    print("   • 4×2mm NdFeB magnet")
    print("   • Dyneema cable routing")
    print("   • 12×12×28mm nylon cradle housing")
