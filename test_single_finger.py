#!/usr/bin/env python3
"""
Test script - Single finger unit to verify FreeCAD is working
Creates one complete finger assembly for testing
"""

import FreeCAD as App
import FreeCADGui as Gui
import Part
import math

print("🔧 Testing FreeCAD with single finger unit...")

# Create new document
doc = App.newDocument("TestFinger")

# Create a simple N20 motor
motor = Part.makeBox(26, 10, 12)  # N20 dimensions
motor_obj = doc.addObject("Part::Feature", "TestMotor")
motor_obj.Shape = motor
motor_obj.ViewObject.ShapeColor = (0.2, 0.2, 0.2)

# Create a brass gear
gear = Part.makeCylinder(3, 2)  # 6mm OD, 2mm thick
gear_obj = doc.addObject("Part::Feature", "TestGear")
gear_obj.Shape = gear
gear_obj.ViewObject.ShapeColor = (1.0, 0.84, 0.0)
gear_obj.Placement.Base = App.Vector(30, 5, 6)

# Create a bearing
bearing_outer = Part.makeCylinder(4, 2.5)  # 8mm OD, 2.5mm thick
bearing_inner = Part.makeCylinder(2.5, 2.7)  # 5mm ID
bearing = bearing_outer.cut(bearing_inner)
bearing_obj = doc.addObject("Part::Feature", "TestBearing")
bearing_obj.Shape = bearing
bearing_obj.ViewObject.ShapeColor = (0.7, 0.7, 0.7)
bearing_obj.Placement.Base = App.Vector(30, 5, 10)

# Recompute and fit view
doc.recompute()
Gui.SendMsgToActiveView("ViewFit")

print("✅ Test finger unit created successfully!")
print("🎯 FreeCAD is working properly!")
print("💡 You should see a motor, gear, and bearing in the 3D view")
