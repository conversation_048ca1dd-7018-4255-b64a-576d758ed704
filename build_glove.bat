@echo off
REM LucidGlove v6 - Windows Build Script
REM Double-click this file to build the FreeCAD project

echo.
echo ========================================
echo  LucidGlove v6 - FreeCAD Project Builder
echo ========================================
echo.

REM Check if FreeCAD is installed
echo Checking FreeCAD installation...
FreeCADCmd --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ERROR: FreeCAD not found!
    echo.
    echo Please install FreeCAD and add it to your PATH:
    echo 1. Download from: https://www.freecad.org/downloads.php
    echo 2. Install FreeCAD
    echo 3. Add FreeCAD installation directory to PATH
    echo    Example: C:\Program Files\FreeCAD 0.21\bin
    echo.
    pause
    exit /b 1
)

echo FreeCAD found! Building project...
echo.

REM Build the project
echo Running: FreeCADCmd create_haptic_glove_project.py
echo.
FreeCADCmd create_haptic_glove_project.py

if errorlevel 1 (
    echo.
    echo ERROR: Build failed!
    echo Check the output above for error details.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  BUILD COMPLETE!
echo ========================================
echo.
echo Files created:
if exist LucidGlove_v6.FCStd (
    echo   [OK] LucidGlove_v6.FCStd - FreeCAD project file
) else (
    echo   [MISSING] LucidGlove_v6.FCStd
)

if exist LucidGlove_v6.stl (
    echo   [OK] LucidGlove_v6.stl - 3D printable mesh
) else (
    echo   [INFO] LucidGlove_v6.stl - Not created (normal)
)

echo.
echo To view the project:
echo 1. Open FreeCAD
echo 2. File -^> Open -^> LucidGlove_v6.FCStd
echo 3. Explore the 3D model
echo.
echo Press any key to exit...
pause >nul
