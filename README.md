# LucidGlove v6 - Complete FreeCAD Workflow

**Fully code-driven FreeCAD workflow** that reproduces the final N20-based finger module, assembles five of them into a glove back-plate, and saves everything to `LucidGlove_v6.FCStd`.

This implementation relies solely on FreeCAD's native Python API, so you can run it headless with **`FreeCADCmd`** or inside the GUI macro editor—no proprietary add-ons required.

## 🚀 Quick Start

```bash
# Build the complete project
make build

# Or run directly with FreeCAD
FreeCADCmd create_haptic_glove_project.py
```

## 📋 Project Specifications

Based on your exact bill of materials:

| Component | Specifications | Quantity | Notes |
|-----------|---------------|----------|-------|
| N20 Motor | 6V 298:1 gear motor, 26×10×12mm | 5× | Pololu/clone compatible |
| Spur Gear | Module 0.5, 10-tooth brass, 6mm OD | 5× | 3mm bore for D-shaft |
| Output Drum | 10mm OD, 6mm thick printed collar | 5× | Integrated bearing pocket |
| MR85 Bearing | 5×8×2.5mm sealed ball bearing | 5× | Press-fit into drum |
| AS5600 Encoder | 12-bit magnetic rotary encoder | 5× | 15×15mm PCB |
| NdFeB Magnet | 4×2mm neodymium magnet | 5× | For encoder sensing |
| Dyneema Cable | 0.8mm diameter, 30lb strength | 5× | Finger actuation |

## 🏗️ Architecture

### Parametric Design
All dimensions are user-tweakable at the top of `create_haptic_glove_project.py`:

```python
# ---------- user-tweakable parameters ----------
motor_len = 26    # mm  (N20 "298:1 MP")
motor_w   = 10
motor_h   = 12
gear_od   = 6
gear_th   = 2
bearing_od= 8
bearing_h = 2.5
finger_gap= 22    # spacing between finger bases
# ----------------------------------------------
```

### Component Assembly Logic

| Component | Model Logic | Real-Part Size | Assembly Notes |
|-----------|-------------|----------------|----------------|
| Motor | `Part.makeBox` | 26×10×12mm | Base component, all others reference |
| Spur Gear | Cylinder minus bore | 6mm Ø, 2mm thick | Mounts on motor D-shaft |
| Output Drum | Larger cylinder | 10mm Ø, 4.5mm | Contains MR85 bearing |
| Bearing | (Referenced only) | 5×8×2.5mm | Press-fit into drum |
| Encoder | PCB representation | 15×10×1.6mm | AS5600 breakout board |

## 🔧 Build System

### Make Targets

```bash
make build          # Build complete FreeCAD project (default)
make clean          # Remove all build artifacts  
make rebuild        # Clean and build
make dev-build      # Build with verbose logging
make export-stl     # Export STL mesh from existing project
make check-freecad  # Verify FreeCAD installation
make help           # Show all available targets
```

### Manual Build

```bash
# Headless build (no GUI)
FreeCADCmd create_haptic_glove_project.py

# GUI build (run inside FreeCAD macro editor)
# 1. Open FreeCAD
# 2. Macro → Macros...
# 3. Create new macro, paste script content
# 4. Execute
```

## 📁 Output Files

- **`LucidGlove_v6.FCStd`** - Complete FreeCAD project file
- **`LucidGlove_v6.stl`** - 3D printable mesh (if ImportGui available)
- **`build.log`** - Verbose build log (dev-build only)

## 🎯 Key Features

### ✅ Fully Parametric
- All dimensions editable at script top
- Regenerate entire model by changing parameters
- Maintains mechanical relationships automatically

### ✅ Production Ready
- Based on real component specifications
- Proper clearances and tolerances
- Ready for 3D printing and assembly

### ✅ Open Source Workflow
- No proprietary add-ons required
- Uses only FreeCAD native Python API
- Runs headless for CI/batch builds

### ✅ Complete Integration
- All 5 finger modules positioned correctly
- Proper spacing and mechanical relationships
- Export-ready for manufacturing

## 🔍 Usage Examples

### View in FreeCAD GUI
```bash
# After building
FreeCAD LucidGlove_v6.FCStd
```

### Modify Parameters
Edit the parameter block in `create_haptic_glove_project.py`:
```python
finger_gap = 25    # Increase spacing between fingers
gear_od = 8        # Use larger gear
motor_len = 30     # Accommodate different motor
```

Then rebuild:
```bash
make rebuild
```

### Export for 3D Printing
```bash
make export-stl
# Creates LucidGlove_v6.stl ready for slicing
```

## 🛠️ Requirements

- **FreeCAD 0.20+** (0.21+ recommended)
- **Python 3.7+** (included with FreeCAD)
- **Windows/Linux/macOS** (cross-platform)

### Installation

**Windows:**
```bash
make install-freecad  # Automated installer (requires admin)
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt install freecad
```

**macOS:**
```bash
brew install freecad
```

## 📚 API References

This implementation uses official FreeCAD APIs:

- [Part Workbench API](https://wiki.freecad.org/Part_API) - Primitive creation
- [FreeCAD Python API](https://wiki.freecad.org/FreeCAD_API) - Document management  
- [Command Line Usage](https://wiki.freecad.org/Start_up_and_Configuration#Running_FreeCAD_without_GUI) - Headless execution
- [Parametric Objects](https://wiki.freecad.org/Manual:Creating_parametric_objects) - Custom properties

## 🎉 Result

A **completely open-source, reproducible, and editable FreeCAD project** that mirrors the final hardware layout. Adjust parameters or add components as your design evolves, regenerate, and your CAD remains in lock-step with the bill-of-materials.

Perfect for:
- ✅ Design iteration and prototyping
- ✅ Manufacturing planning and assembly
- ✅ 3D printing and part production  
- ✅ Documentation and collaboration
- ✅ CI/CD integration for automated builds
