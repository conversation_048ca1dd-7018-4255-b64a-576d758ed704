@echo off
echo 🚀 Creating Haptic VR Glove FreeCAD Project File
echo 📋 Based on your exact bill of materials
echo.
echo 🔧 Components to be created:
echo    • 10× N20 6V 298:1 gear motors with D-shafts
echo    • 10× Module 0.5, 10-tooth brass spur gears  
echo    • 10× AS5600 12-bit magnetic encoders
echo    • 5× MR85-2RS bearings (5×8×2.5mm)
echo    • 5× Nylon cradles (12×12×28mm)
echo    • 5× Output drums with bearing pockets
echo    • 1× ESP32-DevKit-V1 microcontroller
echo    • Dyneema cables and NdFeB magnets
echo    • Complete mechanical assembly
echo.
echo 📁 Generating FreeCAD project file...

"C:\Users\<USER>\Anaconda3\Library\bin\freecad.exe" create_haptic_glove_project.py

echo.
echo ✅ Project file created: HapticVRGlove.FCStd
echo 💡 Open this file in FreeCAD to view the complete assembly
echo 🎯 All components are positioned with correct mechanical relationships
pause
