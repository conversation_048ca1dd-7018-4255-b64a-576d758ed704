#!/usr/bin/env python3
"""
Haptic VR Glove - FreeCAD Macro
Creates a complete haptic glove project with all components
Run this as a macro in FreeCAD
"""

import FreeCAD as App
import FreeCADGui as Gui
import Part
import math

# Create new document
doc = App.newDocument("HapticVRGlove")

print("🚀 Creating Haptic VR Glove Project...")
print("📋 Creating simplified component representations...")

# Component dimensions (from your bill of materials)
MOTOR_LEN = 26.0  # mm
MOTOR_W = 10.0    # mm  
MOTOR_H = 12.0    # mm
FINGER_SPACING = 22.0  # mm

def create_simple_box(doc, name, length, width, height, position, color):
    """Create a simple box component"""
    box = Part.makeBox(length, width, height)
    obj = doc.addObject("Part::Feature", name)
    obj.Shape = box
    obj.ViewObject.ShapeColor = color
    obj.Placement.Base = position
    return obj

def create_simple_cylinder(doc, name, radius, height, position, color):
    """Create a simple cylinder component"""
    cylinder = Part.makeCylinder(radius, height)
    obj = doc.addObject("Part::Feature", name)
    obj.Shape = cylinder
    obj.ViewObject.ShapeColor = color
    obj.Placement.Base = position
    return obj

# Create 5 finger units
all_components = []

for finger in range(1, 6):
    print(f"🖐️  Creating finger unit {finger}/5...")
    
    # Base position for this finger
    base_x = 0
    base_y = (finger - 1) * FINGER_SPACING
    base_z = 0
    
    # Nylon cradle (12×12×28mm)
    cradle = create_simple_box(
        doc, f"Cradle_{finger}", 
        28, 12, 12, 
        App.Vector(base_x, base_y, base_z),
        (0.9, 0.9, 0.7)  # Nylon color
    )
    cradle.Label = f"Cradle_{finger} (12×12×28mm nylon)"
    all_components.append(cradle)
    
    # N20 Motor (26×10×12mm)
    motor = create_simple_box(
        doc, f"Motor_{finger}",
        MOTOR_LEN, MOTOR_W, MOTOR_H,
        App.Vector(base_x + 1, base_y + 1, base_z + 0.5),
        (0.2, 0.2, 0.2)  # Dark gray
    )
    motor.Label = f"Motor_{finger} (N20 6V 298:1)"
    all_components.append(motor)
    
    # Brass gear (6mm diameter, 2mm thick)
    gear = create_simple_cylinder(
        doc, f"Gear_{finger}",
        3.0, 2.0,
        App.Vector(base_x + 30, base_y + 6, base_z + 6),
        (1.0, 0.84, 0.0)  # Gold (brass)
    )
    gear.Label = f"Gear_{finger} (Module 0.5, 10T brass)"
    all_components.append(gear)
    
    # Output drum (10mm diameter, 6mm thick)
    drum = create_simple_cylinder(
        doc, f"Drum_{finger}",
        5.0, 6.0,
        App.Vector(base_x + 32, base_y + 6, base_z + 6),
        (0.0, 0.4, 0.8)  # Blue (3D printed)
    )
    drum.Label = f"Drum_{finger} (6mm collar w/ bearing pocket)"
    all_components.append(drum)
    
    # MR85 Bearing (8mm OD, 2.5mm thick)
    bearing = create_simple_cylinder(
        doc, f"Bearing_{finger}",
        4.0, 2.5,
        App.Vector(base_x + 32, base_y + 6, base_z + 9),
        (0.7, 0.7, 0.7)  # Steel gray
    )
    bearing.Label = f"Bearing_{finger} (MR85-2RS 5×8×2.5mm)"
    all_components.append(bearing)
    
    # AS5600 Encoder PCB (15×15×1.6mm)
    encoder = create_simple_box(
        doc, f"Encoder_{finger}",
        15, 15, 1.6,
        App.Vector(base_x + 45, base_y, base_z + 8),
        (0.0, 0.6, 0.0)  # Green PCB
    )
    encoder.Label = f"Encoder_{finger} (AS5600 12-bit)"
    all_components.append(encoder)
    
    # NdFeB Magnet (4mm diameter, 2mm thick)
    magnet = create_simple_cylinder(
        doc, f"Magnet_{finger}",
        2.0, 2.0,
        App.Vector(base_x + 32, base_y + 6, base_z + 4),
        (0.3, 0.3, 0.3)  # Dark metallic
    )
    magnet.Label = f"Magnet_{finger} (4×2mm NdFeB)"
    all_components.append(magnet)
    
    # Dyneema cable (simple line representation)
    cable_start = App.Vector(base_x + 37, base_y + 6, base_z + 9)
    cable_end = App.Vector(base_x - 20, base_y + 6, base_z - 20)
    
    # Create cable as thin cylinder
    cable_direction = cable_end.sub(cable_start)
    cable_length = cable_direction.Length
    cable = Part.makeCylinder(0.4, cable_length)  # 0.8mm diameter cable
    
    cable_obj = doc.addObject("Part::Feature", f"Cable_{finger}")
    cable_obj.Shape = cable
    cable_obj.ViewObject.ShapeColor = (1.0, 1.0, 0.0)  # Yellow
    cable_obj.Placement.Base = cable_start
    cable_obj.Label = f"Cable_{finger} (Ø0.8mm Dyneema 30lb)"
    all_components.append(cable_obj)

# Create back-of-hand electronics
print("🔌 Creating back-of-hand electronics...")

# ESP32-DevKit-V1 (55×28×1.6mm + components)
esp32 = create_simple_box(
    doc, "ESP32_Main",
    55, 28, 8,  # Include component height
    App.Vector(-80, 44, 20),
    (0.0, 0.6, 0.0)  # Green PCB
)
esp32.Label = "ESP32_Main (ESP32-WROOM-32)"
all_components.append(esp32)

# Main PCB (70×50×1.6mm)
main_pcb = create_simple_box(
    doc, "Main_PCB",
    70, 50, 1.6,
    App.Vector(-90, 34, 15),
    (0.0, 0.6, 0.0)  # Green PCB
)
main_pcb.Label = "Main PCB (70×50mm)"
all_components.append(main_pcb)

# Recompute the document
doc.recompute()

# Fit all objects in view
Gui.SendMsgToActiveView("ViewFit")

# Save the project
import os
project_path = os.path.join(os.getcwd(), "HapticVRGlove.FCStd")
doc.saveAs(project_path)

print()
print("✅ HAPTIC VR GLOVE PROJECT CREATED!")
print(f"📁 Project saved as: {project_path}")
print(f"📊 Total components: {len(all_components)}")
print("🎯 All components positioned with correct spacing")
print("⚙️  Simplified geometry for easy visualization")
print()
print("💡 Project features:")
print("   • 5 complete finger units (40 components)")
print("   • ESP32 microcontroller")
print("   • Main PCB")
print("   • Proper mechanical spacing")
print("   • Color-coded components")
print("   • Ready for detailed design work")

App.Console.PrintMessage("Haptic VR Glove project created successfully!\n")
