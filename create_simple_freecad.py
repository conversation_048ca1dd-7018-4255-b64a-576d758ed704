#!/usr/bin/env python3
"""
Simple FreeCAD Project Creator
Creates a working .FCStd file with basic geometry
"""

import zipfile
import xml.etree.ElementTree as ET
from xml.dom import minidom

def create_simple_document_xml():
    """Create a minimal working Document.xml"""
    
    # Create root with proper namespace
    root = ET.Element("Document")
    root.set("SchemaVersion", "4")
    root.set("ProgramVersion", "0.21")
    root.set("FileVersion", "1")
    
    # Document properties
    properties = ET.SubElement(root, "Properties")
    properties.set("Count", "1")
    
    prop = ET.SubElement(properties, "Property")
    prop.set("name", "Label")
    prop.set("type", "App::PropertyString")
    string_elem = ET.SubElement(prop, "String")
    string_elem.set("value", "HapticVRGlove")
    
    # Objects section with just a few key components
    objects = ET.SubElement(root, "Objects")
    objects.set("Count", "10")  # 5 finger assemblies + 5 motors
    
    # Create 5 finger assemblies
    for finger in range(1, 6):
        # Finger assembly (simplified as single box)
        obj = ET.SubElement(objects, "Object")
        obj.set("type", "Part::Box")
        obj.set("name", f"FingerUnit_{finger}")
        
        props = ET.SubElement(obj, "Properties")
        props.set("Count", "4")
        
        # Label
        label_prop = ET.SubElement(props, "Property")
        label_prop.set("name", "Label")
        label_prop.set("type", "App::PropertyString")
        label_str = ET.SubElement(label_prop, "String")
        label_str.set("value", f"Finger Unit {finger}")
        
        # Length
        len_prop = ET.SubElement(props, "Property")
        len_prop.set("name", "Length")
        len_prop.set("type", "App::PropertyLength")
        len_elem = ET.SubElement(len_prop, "Float")
        len_elem.set("value", "30.0")
        
        # Width
        width_prop = ET.SubElement(props, "Property")
        width_prop.set("name", "Width")
        width_prop.set("type", "App::PropertyLength")
        width_elem = ET.SubElement(width_prop, "Float")
        width_elem.set("value", "15.0")
        
        # Height
        height_prop = ET.SubElement(props, "Property")
        height_prop.set("name", "Height")
        height_prop.set("type", "App::PropertyLength")
        height_elem = ET.SubElement(height_prop, "Float")
        height_elem.set("value", "12.0")
        
        # Placement
        place_prop = ET.SubElement(props, "Property")
        place_prop.set("name", "Placement")
        place_prop.set("type", "App::PropertyPlacement")
        placement = ET.SubElement(place_prop, "PropertyPlacement")
        placement.set("Axis", "0 0 1")
        placement.set("Angle", "0")
        y_pos = (finger - 1) * 25.0  # 25mm spacing
        placement.set("Position", f"0 {y_pos} 0")
        
        # Motor for each finger
        motor_obj = ET.SubElement(objects, "Object")
        motor_obj.set("type", "Part::Box")
        motor_obj.set("name", f"Motor_{finger}")
        
        motor_props = ET.SubElement(motor_obj, "Properties")
        motor_props.set("Count", "4")
        
        # Motor Label
        motor_label = ET.SubElement(motor_props, "Property")
        motor_label.set("name", "Label")
        motor_label.set("type", "App::PropertyString")
        motor_str = ET.SubElement(motor_label, "String")
        motor_str.set("value", f"N20 Motor {finger}")
        
        # Motor dimensions (26×10×12mm)
        motor_len = ET.SubElement(motor_props, "Property")
        motor_len.set("name", "Length")
        motor_len.set("type", "App::PropertyLength")
        motor_len_elem = ET.SubElement(motor_len, "Float")
        motor_len_elem.set("value", "26.0")
        
        motor_width = ET.SubElement(motor_props, "Property")
        motor_width.set("name", "Width")
        motor_width.set("type", "App::PropertyLength")
        motor_width_elem = ET.SubElement(motor_width, "Float")
        motor_width_elem.set("value", "10.0")
        
        motor_height = ET.SubElement(motor_props, "Property")
        motor_height.set("name", "Height")
        motor_height.set("type", "App::PropertyLength")
        motor_height_elem = ET.SubElement(motor_height, "Float")
        motor_height_elem.set("value", "12.0")
        
        # Motor placement
        motor_place = ET.SubElement(motor_props, "Property")
        motor_place.set("name", "Placement")
        motor_place.set("type", "App::PropertyPlacement")
        motor_placement = ET.SubElement(motor_place, "PropertyPlacement")
        motor_placement.set("Axis", "0 0 1")
        motor_placement.set("Angle", "0")
        motor_placement.set("Position", f"35 {y_pos + 2.5} 0")
    
    return root

def create_simple_gui_xml():
    """Create minimal GuiDocument.xml"""
    
    root = ET.Element("Document")
    root.set("SchemaVersion", "1")
    
    # ViewProvider data
    vp_data = ET.SubElement(root, "ViewProviderData")
    vp_data.set("Count", "10")
    
    # Colors for components
    finger_color = "0.0 0.4 0.8"  # Blue
    motor_color = "0.2 0.2 0.2"   # Dark gray
    
    for finger in range(1, 6):
        # Finger unit view provider
        vp = ET.SubElement(vp_data, "ViewProvider")
        vp.set("name", f"FingerUnit_{finger}")
        vp.set("expanded", "0")
        
        props = ET.SubElement(vp, "Properties")
        props.set("Count", "2")
        
        # Shape color
        color_prop = ET.SubElement(props, "Property")
        color_prop.set("name", "ShapeColor")
        color_prop.set("type", "App::PropertyColor")
        color_elem = ET.SubElement(color_prop, "PropertyColor")
        color_elem.set("value", finger_color)
        
        # Visibility
        vis_prop = ET.SubElement(props, "Property")
        vis_prop.set("name", "Visibility")
        vis_prop.set("type", "App::PropertyBool")
        bool_elem = ET.SubElement(vis_prop, "Bool")
        bool_elem.set("value", "true")
        
        # Motor view provider
        motor_vp = ET.SubElement(vp_data, "ViewProvider")
        motor_vp.set("name", f"Motor_{finger}")
        motor_vp.set("expanded", "0")
        
        motor_props = ET.SubElement(motor_vp, "Properties")
        motor_props.set("Count", "2")
        
        # Motor color
        motor_color_prop = ET.SubElement(motor_props, "Property")
        motor_color_prop.set("name", "ShapeColor")
        motor_color_prop.set("type", "App::PropertyColor")
        motor_color_elem = ET.SubElement(motor_color_prop, "PropertyColor")
        motor_color_elem.set("value", motor_color)
        
        # Motor visibility
        motor_vis = ET.SubElement(motor_props, "Property")
        motor_vis.set("name", "Visibility")
        motor_vis.set("type", "App::PropertyBool")
        motor_bool = ET.SubElement(motor_vis, "Bool")
        motor_bool.set("value", "true")
    
    return root

def create_working_freecad_file(filename="HapticVRGlove.FCStd"):
    """Create a working FreeCAD project file"""
    
    print("🚀 Creating working FreeCAD project file...")
    
    # Create XML documents
    doc_xml = create_simple_document_xml()
    gui_xml = create_simple_gui_xml()
    
    # Convert to pretty XML strings
    doc_str = minidom.parseString(ET.tostring(doc_xml)).toprettyxml(indent="  ")
    gui_str = minidom.parseString(ET.tostring(gui_xml)).toprettyxml(indent="  ")
    
    # Create the .FCStd file (ZIP archive)
    with zipfile.ZipFile(filename, 'w', zipfile.ZIP_DEFLATED) as fcstd:
        # Add main documents
        fcstd.writestr("Document.xml", doc_str)
        fcstd.writestr("GuiDocument.xml", gui_str)
    
    print(f"✅ Working FreeCAD project created: {filename}")
    return filename

if __name__ == "__main__":
    print("🔧 Simple FreeCAD Project Creator")
    print("📐 Creating basic haptic glove project...")
    print()
    
    project_file = create_working_freecad_file()
    
    print()
    print("🎉 SUCCESS! Working FreeCAD project file created!")
    print(f"📁 File: {project_file}")
    print("📊 Components included:")
    print("   • 5× Finger units (simplified boxes)")
    print("   • 5× N20 Motors")
    print("   • Proper spacing and positioning")
    print()
    print("💡 To use:")
    print("   1. Open FreeCAD")
    print("   2. File → Open → HapticVRGlove.FCStd")
    print("   3. All components will be visible")
    print("   4. Use this as a starting point for detailed design")
    print()
    print("🔧 This simplified version uses FreeCAD's built-in")
    print("   Part::Box objects which should load without errors.")
